/**
 * BtmListMultGroupPop 搜索渲染工具
 * 用于处理搜索模板的渲染逻辑，替代原有的 hunt 组件相关方法
 */
// 从 ConfigurableForm 引入配置
const {
  SEARCH_CONSTANTS
} = require('../../../components/ConfigurableForm/config/fields');
const {
  SEARCH_TERM_LIST,
  getSearchConfig
} = require('../../../components/ConfigurableForm/config/constants');

/**
 * 搜索常量配置 - 基于 ConfigurableForm 的配置
 */
const SEAR_CONSTANTS = {
  // 单选字段
  RADIO: {
    list: SEARCH_CONSTANTS.RADIO_FIELDS,
    map: {
      mobile_phone_flag: '联系方式',
      fixed_phone_flag: '手机号码',
      email_flag: '联系邮箱',
      tendering_and_bidding: '招投标',
      job_flag: '招聘',
      tax_credit_flag: '纳税信用',
      trade_mark_info_flag: '商标信息',
      android_app: '安卓APP',
      apple_app: '苹果APP',
      applet: '小程序',
      we_chat_official_account: '微信公众号',
      weibo_flag: '微博',
      dishonest_info: '失信信息',
      judgment_doc: '裁判文书',
      administrative_penalty: '行政处罚',
      chattel_mortgage: '动产抵押',
      business_abnormalities: '经营异常',
      copyrights_flag: '软件著作权',
      copyright_of_works: '作品著作权',
      official_website_info: '官网信息',
      icp_filing: 'ICP备案',
      head_ent_flag: '龙头企业'
    }
  },

  // 多选字段
  MULTI_SELECT: {
    list: SEARCH_CONSTANTS.MULTI_SELECT_FIELDS,
    map: {
      ent_expand: '疑似扩张',
      ent_size: '企业规模',
      benefits_assess: '效益评估',
      technology: '科技型企业',
      ent_status_list: '企业状态',
      ent_entity_type: '实体类型',
      insured_num: '从业人数',
      financing_info_list: '融资信息',
      listing_status: '上市状态',
      patent_info: '专利信息'
    }
  },

  // 弹窗字段
  POPUP: {
    list: SEARCH_CONSTANTS.POPUP_FIELDS,
    map: {
      area_code_list: '所在地区',
      industry_code_list: '所属行业',
      ent_type: '企业类型',
      enterprise_license_list: '企业许可',
      industrial_list: '所属产业'
    }
  },

  // 输入框字段
  INPUT: {
    list: SEARCH_CONSTANTS.INPUT_FIELDS,
    map: {
      ent_name: '企业名称'
    }
  },

  // 范围输入字段
  RANGE: {
    list: SEARCH_CONSTANTS.RANGE_INPUT_FIELDS,
    map: {
      reg_capital: '注册资本',
      est_date: '注册时间',
      insured_num: '从业人数'
    },
    tips: {
      reg_capital: {
        min: '最低资本',
        max: '最高资本',
        unit: '万元'
      },
      est_date: {
        min: '最低年限',
        max: '最高年限',
        unit: '年'
      },
      insured_num: {
        min: '最少人数',
        max: '最多人数',
        unit: '人'
      }
    }
  }
};

/**
 * 获取标签数据映射
 * 从 ConfigurableForm 的配置中获取标签数据
 */
function getTagDataMap() {
  try {
    // 尝试从 ConfigurableForm 获取配置
    // 获取完整的搜索配置
    const searchConfig = getSearchConfig('full');
    const searchTermList = searchConfig.searchTermList || SEARCH_TERM_LIST;

    const tagDataMap = {};

    // 直接从 searchTermList 获取数据，因为它包含了完整的 list 信息
    searchTermList.forEach(field => {
      if (field.list && Array.isArray(field.list)) {
        tagDataMap[field.type] = field.list.map(item => ({
          id: item.id,
          name: item.name
        }));
      }
    });

    return tagDataMap;
  } catch (error) {
    console.warn('无法从 ConfigurableForm 获取配置，使用默认配置:', error);
  }
}

/**
 * 从弹窗数据中获取显示名称
 * @param {Array} data - 弹窗选择的数据
 * @returns {string} 显示名称
 */
function getNameFromPopup(data) {
  if (!Array.isArray(data) || data.length === 0) {
    return '';
  }

  // 处理产业链数据的特殊情况
  if (data[0]?.MultipleCelectionSingleSelection) {
    return data[0].name;
  }

  // 处理新的 BtmListMultGroupPop 数据格式（直接包含选中项）
  if (
    data.length > 0 &&
    data[0].code &&
    data[0].name &&
    !data[0].status &&
    !data[0].active
  ) {
    // 新格式：直接是选中的项目数组
    return data.map(item => item.name).join(',');
  }

  // 处理普通弹窗数据（旧格式）
  const checkedItems = [];
  data.forEach((item, _, array) => {
    if (item.status === 'checked' || item.active === true) {
      if (item.parent && item.parent !== '') {
        // 有父级的情况
        const parent = array.find(
          parentItem => parentItem.code === item.parent
        );
        if (parent && (parent.status === 'checked' || parent.active === true)) {
          checkedItems.push(parent);
        } else {
          checkedItems.push(item);
        }
      } else {
        // 没有父级的情况
        checkedItems.push(item);
      }
    }
  });

  // 去重
  const uniqueItems = [];
  const codeSet = new Set();
  checkedItems.forEach(item => {
    if (!codeSet.has(item.code)) {
      codeSet.add(item.code);
      uniqueItems.push(item);
    }
  });

  return uniqueItems.map(item => item.name).join(',');
}

/**
 * 主要的搜索渲染方法
 * @param {Object} searchData - 搜索数据对象
 * @returns {Array} 渲染数组 [{name: '字段名', value: '显示值'}]
 */
function searRender(searchData) {
  if (!searchData || typeof searchData !== 'object') {
    return [];
  }

  const renderArray = [];
  const tagDataMap = getTagDataMap();
  Object.keys(searchData).forEach(key => {
    const value = searchData[key];

    // 跳过空值
    if (
      !value ||
      (Array.isArray(value) && value.length === 0) ||
      (typeof value === 'string' && value.trim() === '')
    ) {
      return;
    }

    // 处理输入框字段
    if (SEAR_CONSTANTS.INPUT.list.includes(key)) {
      renderArray.push({
        name: SEAR_CONSTANTS.INPUT.map[key],
        value: value
      });
    }
    // 处理弹窗字段
    else if (SEAR_CONSTANTS.POPUP.list.includes(key)) {
      if (Array.isArray(value) && value.length > 0) {
        renderArray.push({
          name: SEAR_CONSTANTS.POPUP.map[key],
          value: getNameFromPopup(value)
        });
      }
    }
    // 处理单选字段
    else if (SEAR_CONSTANTS.RADIO.list.includes(key)) {
      if (Array.isArray(value) && value.length > 0) {
        // 获取该字段的标签数据
        const tagData = tagDataMap[key] || [];

        // 尝试在标签数据中查找匹配的选项
        const matchedTag = tagData.find(tag => tag.id === value[0]);

        // 如果找到匹配的选项，使用其名称；否则使用默认的"有"/"无"转换
        const displayValue = matchedTag
          ? matchedTag.name
          : value[0] === 'true'
          ? '有'
          : value[0] === 'false'
          ? '无'
          : value[0];

        renderArray.push({
          name: SEAR_CONSTANTS.RADIO.map[key],
          value: displayValue
        });
      }
    }
    // 处理多选字段
    else if (SEAR_CONSTANTS.MULTI_SELECT.list.includes(key)) {
      if (Array.isArray(value) && value.length > 0) {
        const tagData = tagDataMap[key] || [];
        const displayNames = [];

        value.forEach(val => {
          const matchedTag = tagData.find(
            tag => tag.id === val || tag.id === String(val)
          );
          if (matchedTag) {
            displayNames.push(matchedTag.name);
          } else {
            displayNames.push(val); // 如果找不到对应名称，直接使用原值
          }
        });

        renderArray.push({
          name: SEAR_CONSTANTS.MULTI_SELECT.map[key],
          value: displayNames.join(',')
        });
      }
    }
    // 处理范围输入字段 - 修复多选支持
    else if (SEAR_CONSTANTS.RANGE.list.includes(key)) {
      if (Array.isArray(value) && value.length > 0) {
        // 检查是否有自定义输入
        const hasCustomInput = value.some(item => item.special === true);

        if (hasCustomInput) {
          // 如果有自定义输入，只显示自定义输入
          const customData = value.find(item => item.special === true);
          if (customData) {
            const {start, end} = customData;
            if (start || end) {
              const tips = SEAR_CONSTANTS.RANGE.tips[key];
              let displayValue = '';

              if (start && end) {
                displayValue = `${tips.min}${start}-${tips.max}${end}${tips.unit}`;
              } else if (start) {
                displayValue = `${tips.min}${start}${tips.unit}`;
              } else if (end) {
                displayValue = `${tips.max}${end}${tips.unit}`;
              }

              renderArray.push({
                name: SEAR_CONSTANTS.RANGE.map[key],
                value: displayValue
              });
            }
          }
        } else {
          // 如果没有自定义输入，处理所有标签选择
          const tagData = tagDataMap[key] || [];
          const displayNames = [];

          value.forEach(rangeData => {
            if (rangeData && typeof rangeData === 'object') {
              const {start, end} = rangeData;
              const rangeStr = `${start || ''}$${end || ''}`;

              // 检查是否是预设标签
              const matchedTag = tagData.find(tag => tag.id === rangeStr);

              if (matchedTag) {
                displayNames.push(matchedTag.name);
              } else if (start || end) {
                // 如果不是预设标签但有值，作为自定义范围处理
                const tips = SEAR_CONSTANTS.RANGE.tips[key];
                if (start && end) {
                  displayNames.push(
                    `${tips.min}${start}-${tips.max}${end}${tips.unit}`
                  );
                } else if (start) {
                  displayNames.push(`${tips.min}${start}${tips.unit}`);
                } else if (end) {
                  displayNames.push(`${tips.max}${end}${tips.unit}`);
                }
              }
            }
          });

          if (displayNames.length > 0) {
            renderArray.push({
              name: SEAR_CONSTANTS.RANGE.map[key],
              value: displayNames.join(',')
            });
          }
        }
      }
    }
  });

  return renderArray;
}

module.exports = {
  searRender,
  getNameFromPopup,
  SEAR_CONSTANTS
};
