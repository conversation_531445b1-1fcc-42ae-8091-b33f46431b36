import {
  hotIndustryTypeSelectorApi, // 热点
  classicIndustryExpandListApi, // 经典 - 修复：使用正确的API
  originalIndustryClusterSelectorApi, // 产业图谱
  originalIndustryPurchasedListApi // 产业链图谱 - 已购买的产业链
} from '../../service/industryApi';

// Tab 选项配置
const TAB_OPTIONS = [
  {
    code: 'hot',
    name: '热点产业名单'
  },
  {
    code: 'classic',
    name: '经典产业名单'
  },
  {
    code: 'emerging',
    name: '产业链图谱'
  }
];

// 移除全局变量，数据将存储在组件的 data 中

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    position: {
      type: String,
      value: 'top'
    },
    startDistance: {
      type: String,
      value: '0px'
    },
    defaultCode: {
      type: null,
      value: ''
    },
    top: {
      type: Number,
      value: 0
    },
    zIndex: {
      type: Number,
      value: 10
    }
  },

  data: {
    parentList: [],
    activeChildList: [],
    selectedPath: {
      parent: null,
      child: null
    },
    finalSelection: null,
    readyToShow: false,
    tabs: TAB_OPTIONS,
    activeTabIndex: 0, // 默认选中第一个tab（热点产业名单）
    currentDataSource: [],
    // 数据源映射
    dataSourceMap: {
      hot: [],
      classic: [],
      emerging: []
    },
    // 状态记忆：只记录已确认的选择（点击第二级时确认）
    confirmedSelection: {
      tabIndex: 0, // 已确认选择的Tab索引
      selectedCode: null, // 已确认选择的code
      hasConfirmed: false // 是否有已确认的选择
    }
  },

  lifetimes: {
    async attached() {
      this.setData({readyToShow: false});

      // 初始化数据源 - 从 API 获取真实数据
      await this.initializeDataSources();

      // 使用默认数据源初始化组件
      this.initializeData(this.data.currentDataSource);
    }
  },

  observers: {
    visible: function (bl) {
      if (bl) {
        const {confirmedSelection, defaultCode} = this.data;

        // 确保数据已初始化
        if (this.data.parentList.length === 0) {
          this.initializeData(this.data.currentDataSource);
        }

        // 如果有已确认的选择，恢复到该状态
        if (confirmedSelection.hasConfirmed) {
          this.setData({
            activeTabIndex: confirmedSelection.tabIndex
          });
          this.switchTab({
            currentTarget: {
              dataset: {
                index: confirmedSelection.tabIndex
              }
            }
          });
        } else if (defaultCode && this.data.dataSourceMap) {
          // 如果有 defaultCode 且数据已初始化，处理回显
          this.setData({readyToShow: false});
          const extractedCode = this.extractCodeFromDefaultCode(defaultCode);
          if (extractedCode) {
            this.setDefaultSelectWithTab(extractedCode);
          } else {
            this.setData({readyToShow: true});
          }
        }
      }
    },

    // 监听 defaultCode 变化，用于回显选中状态
    defaultCode: function (code) {
      if (code && this.data.visible && this.data.dataSourceMap) {
        this.setData({readyToShow: false});
        const extractedCode = this.extractCodeFromDefaultCode(code);
        // 只有当提取出有效的code时才设置默认选择
        if (extractedCode) {
          this.setDefaultSelectWithTab(extractedCode);
        } else {
          // 没有有效code时直接显示
          this.setData({readyToShow: true});
        }
      }
    }
  },

  methods: {
    // 初始化数据源 - 从 API 获取真实数据
    async initializeDataSources() {
      try {
        // 并行获取三个数据源
        const [hotData, classicData, emergingData] = await Promise.all([
          this.getHotIndustryData(),
          this.getClassicIndustryData(),
          this.getEmergingIndustryData()
        ]);

        // 格式化数据
        const formattedHotData = this.formatApiDataToComponentFormat(
          hotData,
          'hot'
        );
        const formattedClassicData = this.formatApiDataToComponentFormat(
          classicData,
          'classic'
        );
        // emergingData 已经在 getEmergingIndustryData 中格式化过了
        const formattedEmergingData = emergingData;

        this.setData({
          'dataSourceMap.hot': formattedHotData,
          'dataSourceMap.classic': formattedClassicData,
          'dataSourceMap.emerging': formattedEmergingData,
          currentDataSource: formattedHotData // 默认使用热点产业数据
        });
      } catch (error) {
        console.error('初始化数据源失败:', error);
        // 使用空数据作为fallback
        this.setData({
          'dataSourceMap.hot': [],
          'dataSourceMap.classic': [],
          'dataSourceMap.emerging': [],
          currentDataSource: []
        });
      }
    },

    // 格式化 API 数据为组件需要的格式
    formatApiDataToComponentFormat(apiData, type) {
      if (!Array.isArray(apiData)) {
        return [];
      }
      return apiData.map(item => {
        const mainName =
          item.hot_industrial_name || item.classic_type_name || item.chain_name;
        const children = item?.chains || item?.child || item?.list;
        return {
          code: item?.code || item?.chain_code || String(item.classic_type_id),
          name: mainName,
          type,
          children: Array.isArray(children)
            ? children.map(chain => {
                const tempObj = {
                  code: `${chain?.code || chain?.chain_code || chain.id}`,
                  name:
                    chain?.name ||
                    chain?.chain_name ||
                    chain.classic_industrial_name,
                  type
                };
                if (Object.keys(chain).includes('purchased')) {
                  tempObj.purchased = chain.purchased;
                }
                return tempObj;
              })
            : []
        };
      });
    },

    // 从不同格式的defaultCode中提取code值（支持字符串、对象、数组格式）
    extractCodeFromDefaultCode(defaultCode) {
      if (!defaultCode) return '';
      if (typeof defaultCode === 'string') {
        return defaultCode;
      }
      if (Array.isArray(defaultCode)) {
        if (defaultCode.length === 0) return '';
        const firstItem = defaultCode[0];
        if (typeof firstItem === 'string') {
          return firstItem;
        }
        if (typeof firstItem === 'object' && firstItem.code) {
          return firstItem.code;
        }
        return '';
      }
      if (typeof defaultCode === 'object' && defaultCode.code) {
        return defaultCode.code;
      }
      return '';
    },

    // 智能默认选择：根据defaultCode自动查找所属Tab并切换，然后设置选中状态
    setDefaultSelectWithTab(targetCode) {
      if (!targetCode) return;

      console.log('开始查找 targetCode:', targetCode);

      // 在所有数据源中查找匹配的code（支持父级和子级）
      let foundTab = null; // 找到的Tab索引
      let foundData = null; // 找到的数据（包含parent和child信息）

      // 遍历所有Tab的数据源
      const {dataSourceMap} = this.data;
      console.log('当前数据源:', dataSourceMap);

      for (let tabIndex = 0; tabIndex < TAB_OPTIONS.length; tabIndex++) {
        const tabCode = TAB_OPTIONS[tabIndex].code;
        const dataSource = dataSourceMap[tabCode];

        console.log(`在 ${tabCode} Tab 中查找...`, dataSource?.length || 0);

        // 在当前数据源中查找匹配的code
        const result = this.findCodeInDataSource(dataSource, targetCode);
        if (result) {
          foundTab = tabIndex;
          foundData = result;
          console.log(`找到匹配项在 ${tabCode} Tab:`, result);
          break;
        }
      }

      if (foundTab !== null && foundData) {
        console.log('切换到 Tab:', foundTab);

        // 切换到对应的Tab
        this.setData({
          activeTabIndex: foundTab
        });

        // 切换Tab的数据源
        this.switchTab({
          currentTarget: {
            dataset: {
              index: foundTab
            }
          }
        });

        // 延迟设置选中状态，确保Tab切换完成
        setTimeout(() => {
          this.setDefaultSelect(targetCode);
        }, 50);
      } else {
        console.log('没找到匹配的code，显示默认状态');
        // 没找到匹配的code，显示默认状态
        this.setData({readyToShow: true});
      }
    },

    // 在数据源中查找指定的code
    findCodeInDataSource(dataSource, targetCode) {
      if (!Array.isArray(dataSource)) {
        console.log('数据源不是数组:', dataSource);
        return null;
      }

      console.log(
        '在数据源中查找:',
        targetCode,
        '数据源长度:',
        dataSource.length
      );

      for (let parent of dataSource) {
        console.log('检查父级:', parent.code, parent.name);

        // 检查父级code
        if (parent.code === targetCode) {
          console.log('找到父级匹配:', parent);
          return {
            parent: parent,
            child: null
          };
        }

        // 检查子级code
        if (Array.isArray(parent.children)) {
          console.log('检查子级，数量:', parent.children.length);
          for (let child of parent.children) {
            console.log('检查子级:', child.code, child.name);
            if (child.code === targetCode) {
              console.log('找到子级匹配:', child);
              return {
                parent: parent,
                child: child
              };
            }
          }
        }
      }

      console.log('未找到匹配项');
      return null;
    },

    // 设置默认选中状态
    setDefaultSelect(targetCode) {
      const foundData = this.findCodeInDataSource(
        this.data.currentDataSource,
        targetCode
      );

      if (foundData) {
        if (foundData.child) {
          // 选中子级
          this.updateSelectionState(foundData.child.code);
        } else {
          // 选中父级（激活父级，显示其子项）
          const parentIndex = this.data.parentList.findIndex(
            p => p.code === foundData.parent.code
          );
          if (parentIndex !== -1) {
            this.selectParent({
              currentTarget: {
                dataset: {
                  code: foundData.parent.code,
                  name: foundData.parent.name,
                  index: parentIndex
                }
              }
            });
          }
        }
      }

      this.setData({readyToShow: true});
    },

    // 获取热点产业数据
    async getHotIndustryData() {
      try {
        const response = await hotIndustryTypeSelectorApi();
        return response || [];
      } catch (error) {
        console.error('获取热点产业数据失败:', error);
        return [];
      }
    },

    // 获取经典产业数据
    async getClassicIndustryData() {
      try {
        const response = await classicIndustryExpandListApi();
        return response || [];
      } catch (error) {
        console.error('获取经典产业数据失败:', error);
        return [];
      }
    },

    // 获取产业链图谱数据
    async getEmergingIndustryData() {
      try {
        // 并行获取已购买和完整的产业链数据
        const [purchasedData, originalData] = await Promise.all([
          originalIndustryPurchasedListApi(),
          originalIndustryClusterSelectorApi()
        ]);

        // 构造"已购买"分组数据
        const purchasedGroup = this.formatApiDataToComponentFormat(
          [
            {
              hot_industrial_name: '已购买',
              chains: purchasedData || []
            }
          ],
          'chainMap'
        );

        // 格式化原始产业链数据
        const originalGroup = this.formatApiDataToComponentFormat(
          originalData || [],
          'chainMap'
        );

        // 合并数据：已购买在前，原始数据在后
        return [...purchasedGroup, ...originalGroup];
      } catch (error) {
        console.error('获取产业链图谱数据失败:', error);
        return [];
      }
    },

    // 初始化数据
    initializeData(dataSource) {
      if (!dataSource || dataSource.length === 0) {
        this.setData({
          parentList: [],
          activeChildList: [],
          readyToShow: true
        });
        return;
      }

      // 处理父级列表
      const parentList = dataSource.map((item, index) => ({
        ...item,
        active: index === 0,
        selected: false
      }));

      // 处理子级列表（默认显示第一个父级的子项）
      const activeChildList = dataSource[0]?.children || [];

      this.setData({
        parentList,
        activeChildList,
        readyToShow: true
      });
    },

    // Tab 切换
    switchTab(e) {
      const index = e.currentTarget.dataset.index;
      const tabCode = this.data.tabs[index].code;
      const newDataSource = this.data.dataSourceMap[tabCode];

      this.setData({
        activeTabIndex: index,
        currentDataSource: newDataSource
      });

      // 重新初始化数据
      this.initializeData(newDataSource);
    },

    // 选择父级
    selectParent(e) {
      const {code, name, index} = e.currentTarget.dataset;
      const {parentList, currentDataSource} = this.data;

      // 更新父级列表的激活状态
      const updatedParentList = parentList.map((item, idx) => ({
        ...item,
        active: idx === index
      }));

      // 获取对应的子级列表
      const selectedParent = currentDataSource.find(item => item.code === code);
      const activeChildList = selectedParent?.children || [];

      this.setData({
        parentList: updatedParentList,
        activeChildList,
        'selectedPath.parent': {code, name}
      });
    },

    // 选择子级
    selectChild(e) {
      const {code, name, purchased} = e.currentTarget.dataset;
      const {activeTabIndex} = this.data;

      // 如果是产业链图谱且未购买，触发VIP提示
      if (activeTabIndex === 2 && !purchased) {
        this.triggerEvent('vip');
        return;
      }

      // 更新选中状态的视觉反馈
      this.updateSelectionState(code);

      // 记录已确认的选择
      this.setData({
        'confirmedSelection.tabIndex': activeTabIndex,
        'confirmedSelection.selectedCode': code,
        'confirmedSelection.hasConfirmed': true
      });

      // 延迟触发选择事件，让用户看到选中效果
      setTimeout(() => {
        // 触发选择事件 - 修复事件参数格式
        this.triggerEvent('submit', {
          selection: {code, name}
        });

        // 关闭弹窗
        this.close();
      }, 200);
    },

    // 更新选中状态的视觉反馈
    updateSelectionState(selectedCode) {
      let {parentList, activeChildList} = this.data;

      // 找到选中的子级对应的父级
      let parentOfChild = null;
      for (let parent of parentList) {
        if (
          parent.children &&
          parent.children.some(child => child.code === selectedCode)
        ) {
          parentOfChild = parent;
          break;
        }
      }

      if (parentOfChild) {
        // 更新子级选中状态
        activeChildList = activeChildList.map(item => ({
          ...item,
          selected: item.code === selectedCode
        }));

        // 更新父级状态
        parentList = parentList.map(item => ({
          ...item,
          selected: item.code === parentOfChild.code,
          active: item.code === parentOfChild.code
        }));

        // 立即更新UI显示选中效果
        this.setData({
          parentList,
          activeChildList
        });
      }
    },

    // 关闭弹窗
    close() {
      this.triggerEvent('close');
    }
  }
});
