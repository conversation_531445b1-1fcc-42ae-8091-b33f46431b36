<!--components/SingleSelect/index.wxml-->
<HalfScreenPop
  showCloseBtn="{{false}}"
  visible="{{visible}}"
  position="{{position}}"
  bindclose="close"
  startDistance="{{startDistance}}"
  disableAnimation="{{true}}"
  _maskClosable="{{true}}"
  zIndex="{{zIndex}}"
  showFooter="{{false}}"
>
  <view
    slot="customContent"
    class="area"
    style="margin-top:{{top || 0}}px; min-height: 760rpx;"
  >
    <!-- Tab 头部 -->
    <view class="tab-header">
      <view
        class="tab-item {{activeTabIndex === index ? 'active' : ''}}"
        wx:for="{{tabs}}"
        wx:key="code"
        data-index="{{index}}"
        bindtap="switchTab"
      >
        <text class="tab-text">{{item.name}}</text>
        <view
          class="tab-line {{activeTabIndex === index ? 'show' : ''}}"
        ></view>
      </view>
    </view>
    <!-- 内容容器 -->
    <view class="select-container">
      <!-- 占位内容 -->
      <view wx:if="{{!readyToShow}}" class="placeholder-content">
        <view class="placeholder-text">数据加载中...</view>
      </view>

      <!-- 主要内容 - 只有在准备好时才显示 -->
      <view wx:else class="select-wrap">
        <!-- 父级列表 -->
        <view class="list parent-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item parent {{item.active && 'active'}} {{item.selected && 'selected'}}"
              bindtap="selectParent"
              data-code="{{item.code}}"
              data-name="{{item.name}}"
              data-index="{{index}}"
              wx:for="{{parentList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
            </view>
          </scroll-view>
        </view>

        <!-- 子级列表 -->
        <view class="list child-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item child {{item.selected && 'selected'}}"
              bindtap="selectChild"
              data-code="{{item.code}}"
              data-name="{{item.name}}"
              data-purchased="{{item.purchased}}"
              wx:for="{{activeChildList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
              <image
                class="checkmark {{item.selected && 'show'}}"
                wx:if="{{item.selected}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
              ></image>

              <!-- 未购买的锁定图标 -->
              <image
                class="checkmark {{!item.purchased && 'show'}}"
                wx:if="{{activeTabIndex === 2 && !item.purchased}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_lock.png"
              ></image>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</HalfScreenPop>
