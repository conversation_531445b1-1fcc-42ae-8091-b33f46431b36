import { home } from '../../service/api';
import { getHeight } from '../../utils/height';
import { preventActive } from '../../utils/util';
import { getPx } from '../../utils/formate';
// 引入ConfigurableForm的数据处理工具
import {
  handleMultiple,
  handleData,
  getNameFromPop
} from '../ConfigurableForm/utils/data-processor';
import { hasPrivile } from '../../utils/route';
import { collect } from '../../utils/mixin/collect';

const app = getApp();
let PAGESIZE = 10;

Component({
  properties: {
    // 外部筛选状态（用于回填数据）
    externalFilterState: {
      type: Object,
      value: {},
      observer(val) {
        if (val && Object.keys(val).length > 0) {
          this.processExternalFilterState(val);
        }
      }
    },
    // 排除的筛选字段
    excludeFields: {
      type: Array,
      value: ['area_code_list', 'industrial_list']
    },
    // 是否显示地图模式按钮
    showMapMode: {
      type: Boolean,
      value: false
    },
    // 固定标题
    fixedTitle: {
      type: String,
      value: '企业搜索'
    },
    // API 函数名
    requestFunction: {
      type: String,
      value: 'home.portrait'
    }
  },

  data: {
    isLogin: false,
    // 筛选相关
    dropDownMenuTitle: ['全国', '全部行业', '更多筛选'],
    heightParams: {},
    filtrateHeight: 'auto',
    
    // 企业列表相关
    enterpriseList: [],
    totalCount: 0,
    requestParams: {},
    computedHeight: 600,
    
    // 弹窗相关
    showContact: false,
    showAddress: false,
    vipVisible: false,
    activeEntId: '',
    location: {},
    locationTxt: '',
    addmarkers: [],
    
    // VIP 相关
    isCheckVip: true
  },

  lifetimes: {
    attached() {
      this.setData({
        isLogin: app.isLogin()
      });
      this.calculateHeight();
    }
  },

  methods: {
    /**
     * 处理外部筛选状态
     */
    processExternalFilterState(externalFilterState) {
      // 转换为 SerDropDownMenu 需要的格式
      const heightParams = this.convertFromExternalFilterState(externalFilterState);
      
      // 设置标题
      this.updateDropDownMenuTitles(heightParams);
      
      this.setData({
        heightParams,
        requestParams: handleData(heightParams)
      });
    },

    /**
     * 将 externalFilterState 转换为 heightParams 格式
     */
    convertFromExternalFilterState(externalFilterState) {
      const heightParams = {};
      
      // 处理地区数据
      if (externalFilterState.regionData) {
        heightParams.area_code_list = [externalFilterState.regionData];
      }
      
      // 处理产业链数据
      if (externalFilterState.industrial_list) {
        heightParams.industrial_list = [externalFilterState.industrial_list];
      } else if (externalFilterState.classic_industry_code_list) {
        heightParams.industrial_list = [externalFilterState.classic_industry_code_list];
      }
      
      // 处理其他筛选条件
      if (externalFilterState.filterParams) {
        Object.assign(heightParams, externalFilterState.filterParams);
      }
      
      return heightParams;
    },

    /**
     * 更新下拉菜单标题
     */
    updateDropDownMenuTitles(heightParams) {
      let { dropDownMenuTitle } = this.data;
      
      // 地区标题
      if (heightParams.area_code_list?.length > 0) {
        let str = handleMultiple(heightParams.area_code_list);
        dropDownMenuTitle[0] = getNameFromPop(str, { slice: true });
      }
      
      // 产业标题
      if (heightParams.industrial_list?.length > 0) {
        const item = heightParams.industrial_list[0];
        dropDownMenuTitle[1] = item.name || '所属产业';
      }
      
      this.setData({ dropDownMenuTitle });
    },

    /**
     * 筛选回调
     */
    onFlitter(e) {
      const obj = e.detail;
      let { dropDownMenuTitle } = this.data;
      
      // 更新标题
      dropDownMenuTitle[0] = obj.name1 || '全国';
      dropDownMenuTitle[1] = obj.name2 || '全部行业';
      
      // 处理筛选参数
      const requestParams = {
        page_index: 1,
        page_size: PAGESIZE,
        ...obj
      };
      
      // 移除显示用的字段
      delete requestParams.name1;
      delete requestParams.name2;
      delete requestParams.isFilter;
      
      this.setData({
        dropDownMenuTitle,
        requestParams,
        enterpriseList: []
      });
      
      // 触发数据更新
      this.triggerEvent('filterchange', requestParams);
    },

    /**
     * 数据变化回调
     */
    onDataChange(e) {
      const { list, total } = e.detail;
      this.setData({
        enterpriseList: list || [],
        totalCount: total || 0
      });
      
      this.triggerEvent('datachange', { list, total });
    },

    /**
     * 滚动到底部
     */
    onScrollToLower(e) {
      this.triggerEvent('scrolltolower', e.detail);
    },

    /**
     * 滚动事件
     */
    onScroll(e) {
      this.triggerEvent('scroll', e.detail);
    },

    /**
     * 卡片操作
     */
    onCardAction(e) {
      this.triggerEvent('cardaction', e.detail);
    },

    /**
     * 标题点击
     */
    handleTit(e) {
      this.triggerEvent('titleclick', e.detail);
    },

    /**
     * VIP 弹窗
     */
    vipPop() {
      this.setData({ vipVisible: true });
      this.triggerEvent('vip');
    },

    /**
     * 计算高度
     */
    calculateHeight() {
      getHeight(this, ['.drop-menu'], data => {
        const { screeHeight, res } = data;
        let h1 = res[0]?.top || 0;
        let h2 = res[0]?.height || 0;
        this.setData({
          computedHeight: screeHeight - h1 - h2 - getPx(96),
          filtrateHeight: screeHeight - h1
        });
      });
    }
  }
});
