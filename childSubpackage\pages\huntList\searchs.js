import {home, common} from '../../../service/api';
import {getHeight} from '../../../utils/height';
import {preventActive} from '../../../utils/util';
import {getPx, formatDate, handleSearchHight} from '../../../utils/formate';
// 引入ConfigurableForm的数据处理工具
import {
  handleMultiple,
  handleData,
  getNameFromPop
} from '../../../components/ConfigurableForm/utils/data-processor';
import {hasPrivile} from '../../../utils/route';
import {collect} from '../../../utils/mixin/collect';
const app = getApp();
let PAGESIZE = 10;
Page({
  data: {
    isLogin: false,
    // 搜索相关
    historyList: [],
    browsingHistory: [], //浏览历史
    timer: null,
    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    // 浏览历史高度
    scrollHeight: 'auto',
    filtrateHeight: 'auto',
    statusBarHeight: 'auto',
    // BusinessListComponent 相关
    externalFilterState: {},
    requestFunction: 'home.portrait', // API 函数名
    // 卡片 下拉加载更多
    cardHeight: '500',
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: PAGESIZE //每页多少条
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsNull: false, // list长度是否为0
    bazaarIsTriggered: false, // 下拉刷新状态

    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: ''
    },
    locationMap: {},
    // 高级搜索参数
    heightParams: {},
    isHeightParams: false
  },
  onShow() {
    this.scrollH();
    const {login} = app.globalData;
    this.setData({
      isLogin: login
    });
  },
  onLoad(options) {
    let heightParams = options?.str;

    // 说明是从高级搜索那边过来的
    if (heightParams) {
      heightParams = JSON.parse(decodeURIComponent(heightParams));

      // 转换为 BusinessListComponent 需要的格式
      const externalFilterState =
        this.convertToExternalFilterState(heightParams);

      this.setData({
        externalFilterState
      });
    }
  },

  /**
   * 将高级搜索的参数转换为 BusinessListComponent 需要的 externalFilterState 格式
   */
  convertToExternalFilterState(heightParams) {
    const externalFilterState = {
      filterParams: {}
    };

    // 处理地区数据
    if (heightParams.area_code_list?.length > 0) {
      externalFilterState.regionData = heightParams.area_code_list[0];
    }

    // 处理产业链数据
    if (heightParams.industrial_list?.length > 0) {
      const item = heightParams.industrial_list[0];
      if (item.tabCode === 'classic') {
        externalFilterState.classic_industry_code_list = item;
      } else {
        externalFilterState.industrial_list = item;
      }
    }

    // 处理其他筛选条件（企业名称、行业等）
    const filterParams = {};

    // 企业名称
    if (heightParams.ent_name) {
      filterParams.ent_name = heightParams.ent_name;
    }

    // 行业
    if (heightParams.industry_code_list?.length > 0) {
      filterParams.industry_code_list = heightParams.industry_code_list;
    }

    // 其他字段
    Object.keys(heightParams).forEach(key => {
      if (
        ![
          'area_code_list',
          'industrial_list',
          'ent_name',
          'industry_code_list'
        ].includes(key)
      ) {
        filterParams[key] = heightParams[key];
      }
    });

    externalFilterState.filterParams = filterParams;

    return externalFilterState;
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {bazaarlist, bazaarHasData, bazaarParms} = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    // 计算卡片高度
    this.cardHeight();
    //
    //处理请求参数
    const tempParams = {
      ...bazaarParms,
      current_page: bazaarParms.page_index
    };
    delete bazaarParms.page_size;
    home
      .portrait(tempParams)
      .then(res => {
        let items = res?.list || [];
        let count = res?.total || [];
        let ary = [];
        if (items.length < bazaarParms.page_size) bazaarHasData = false;
        ary = items.map((item, index) => {
          item.register_date = formatDate(item.register_date, 'yyyy-MM-dd');
          item.heightKey = bazaarParms['ent_name'];
          return item;
        });
        ary = handleSearchHight(ary, 'ent_name', bazaarParms['ent_name']);
        that.setData(
          {
            bazaarlist: bazaarlist.concat(ary),
            bazaarHasData,
            count: count || 0,
            bazaarIsFlag: true
          },
          () => {
            if (!that.data.bazaarlist.length)
              that.setData({
                bazaarIsNull: true,
                bazaarHasData: true
              });
          }
        );
        callback && callback();
      })
      .catch(err => {
        callback && callback();
        app.showToast(err?.data?.message || '获取数据失败!请稍后再试');
        this.setData({
          bazaarIsNull: true
        });
      });
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading();
    let {bazaarParms} = that.data;
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: PAGESIZE
    };
    that.setData(
      {
        bazaarParms: obj,
        bazaarlist: [],
        bazaarHasData: true
      },
      () =>
        that.initGetList(() => {
          that.setData({
            bazaarIsTriggered: false
          });
          wx.hideNavigationBarLoading();
        })
    );
  },
  //加载更多
  async bazaarloadMore() {
    if (!app.isLogin()) {
      this.setData({
        count: 5
      });
      return;
    }
    // 判断vip类型 主要是针对普通用户
    if (this.data.bazaarlist.length >= 20) {
      let permission = await hasPrivile({
        packageType: true
      });
      this.setData({
        permission
      });
      if (permission == '普通VIP') return;
    }

    let {bazaarParms, bazaarHasData, bazaarIsFlag} = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData(
      {
        bazaarParms
      },
      () => this.initGetList()
    );
  },
  // 筛选回调
  onFlitter(e) {
    console.log('筛选参数:', e.detail);
  },

  // 数据变化回调
  onDataChange(e) {
    console.log('数据变化:', e.detail);
  },
  //----------
  // 动态获取页面高度
  cardHeight() {
    var that = this;
    getHeight(that, ['.drop-menu'], data => {
      const {screeHeight, res} = data;
      let h1 = res[0]?.top;
      let h2 = res[0]?.height;
      this.setData({
        cardHeight: screeHeight - h1 - h2 - getPx(96)
      });
    });
  },
  scrollH() {
    var that = this;
    getHeight(
      that,
      ['.searchs', '.search_a', '.drop-menu', '.his_title'],
      data => {
        const {screeHeight, res, statusBarHeight} = data;
        let h1 = res[0]?.height || 0;
        let h2 = res[1]?.height || 0;
        let h4 = res[3]?.height || 0;
        // 处理search外剩余的高度
        let filtrateHeight = screeHeight - h1;
        // 浏览历史的滚动高度
        let scrollHeight = screeHeight - h1 - h2 - h4;
        that.setData({
          scrollHeight: scrollHeight,
          filtrateHeight: filtrateHeight,
          statusBarHeight: statusBarHeight
        });
      }
    );
  },
  // 卡片点击回调
  goto(e) {
    const {item} = e.currentTarget.dataset;
    console.log('点击跳转到详情');
  },
  async onCard(data) {
    let that = this;
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      let {bazaarlist} = this.data;
      const type = data.detail.type;
      const comDetail = data.detail.data;
      const index = bazaarlist.findIndex(
        item => item.ent_id == comDetail.ent_id
      ); //comDetail.index
      // console.log(index)
      // 处理收藏
      // return
      if (type == 'collect') {
        collect(that, comDetail, 'bazaarlist');
      } else if (type == 'relation') {
        let contactRes = await common.contact(
          comDetail.ent_id,
          encodeURI('types=1,2')
        );
        console.log('联系方式res', contactRes);
        if (contactRes.length > 0) {
          this.setData({
            contactList: contactRes,
            showContact: true,
            activeEntId: comDetail.ent_id
          });
        } else {
          app.showToast('暂无联系方式', 'none', 1300);
          this.setData({
            contactList: [],
            showContact: false
          });
        }
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon
          },
          locationTxt: comDetail.register_address,
          addmarkers: [
            {
              id: 1,
              latitude: +comDetail.location.lat,
              longitude: +comDetail.location.lon,
              iconPath:
                'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
              width: 20,
              height: 20
            }
          ],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        });
      }
    });
  },
  goMap() {
    const {locationMap} = this.data;
    wx.openLocation(locationMap);
  },
  onCloseContact() {
    this.setData({
      showContact: false
    });
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
    // console.log(item.contact_data)
    wx.makePhoneCall({
      phoneNumber: item.contact_data
    });
  },
  // 去详情页面
  goDetail(e) {
    let {enterprise_id} = e.currentTarget.dataset.item;
    const url = encodeURIComponent(
      `https://reporth5.handidit.com?entId=${enterprise_id}`
    );
    // const url = encodeURIComponent(`http://***************:8081?entId=${enterprise_id}`)
    // const url = encodeURIComponent(`http://j-h5-report.ihdwork.com?entId=${enterprise_id}`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`);
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  },
  login() {
    const url = '/companyPackage/pages/searchs/searchs';
    app.route(this, `/pages/login/login?url=${url}`);
  }
});
