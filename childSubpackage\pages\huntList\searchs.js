import {home, common} from '../../../service/api';
import {getHeight} from '../../../utils/height';
import {preventActive} from '../../../utils/util';
import {getPx, formatDate, handleSearchHight} from '../../../utils/formate';
// 引入ConfigurableForm的数据处理工具
import {
  handleMultiple,
  handleData,
  getNameFromPop
} from '../../../components/ConfigurableForm/utils/data-processor';
import {hasPrivile} from '../../../utils/route';
import {collect} from '../../../utils/mixin/collect';
const app = getApp();
let PAGESIZE = 10;
Page({
  data: {
    isLogin: false,
    // EnterpriseSearchComponent 相关
    externalFilterState: {},

    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: ''
    },
    locationMap: {},
    // 高级搜索参数
    heightParams: {},
    isHeightParams: false
  },
  onShow() {
    const {login} = app.globalData;
    this.setData({
      isLogin: login
    });
  },
  onLoad(options) {
    let heightParams = options?.str;

    // 说明是从高级搜索那边过来的
    if (heightParams) {
      heightParams = JSON.parse(decodeURIComponent(heightParams));

      // 转换为 BusinessListComponent 需要的格式
      const externalFilterState =
        this.convertToExternalFilterState(heightParams);

      this.setData({
        externalFilterState
      });
    }
  },

  /**
   * 将高级搜索的参数转换为 BusinessListComponent 需要的 externalFilterState 格式
   */
  convertToExternalFilterState(heightParams) {
    const externalFilterState = {
      filterParams: {}
    };

    // 处理地区数据
    if (heightParams.area_code_list?.length > 0) {
      externalFilterState.regionData = heightParams.area_code_list[0];
    }

    // 处理产业链数据
    if (heightParams.industrial_list?.length > 0) {
      const item = heightParams.industrial_list[0];
      if (item.tabCode === 'classic') {
        externalFilterState.classic_industry_code_list = item;
      } else {
        externalFilterState.industrial_list = item;
      }
    }

    // 处理其他筛选条件（企业名称、行业等）
    const filterParams = {};

    // 企业名称
    if (heightParams.ent_name) {
      filterParams.ent_name = heightParams.ent_name;
    }

    // 行业
    if (heightParams.industry_code_list?.length > 0) {
      filterParams.industry_code_list = heightParams.industry_code_list;
    }

    // 其他字段
    Object.keys(heightParams).forEach(key => {
      if (
        ![
          'area_code_list',
          'industrial_list',
          'ent_name',
          'industry_code_list'
        ].includes(key)
      ) {
        filterParams[key] = heightParams[key];
      }
    });

    externalFilterState.filterParams = filterParams;

    return externalFilterState;
  },
  //获取小米列表
  initGetList(callback) {
    const that = this;
    let {bazaarlist, bazaarHasData, bazaarParms} = that.data;
    that.setData({
      bazaarIsNull: false,
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    // 计算卡片高度
    this.cardHeight();
    //
    //处理请求参数
    const tempParams = {
      ...bazaarParms,
      current_page: bazaarParms.page_index
    };
    delete bazaarParms.page_size;
    home
      .portrait(tempParams)
      .then(res => {
        let items = res?.list || [];
        let count = res?.total || [];
        let ary = [];
        if (items.length < bazaarParms.page_size) bazaarHasData = false;
        ary = items.map((item, index) => {
          item.register_date = formatDate(item.register_date, 'yyyy-MM-dd');
          item.heightKey = bazaarParms['ent_name'];
          return item;
        });
        ary = handleSearchHight(ary, 'ent_name', bazaarParms['ent_name']);
        that.setData(
          {
            bazaarlist: bazaarlist.concat(ary),
            bazaarHasData,
            count: count || 0,
            bazaarIsFlag: true
          },
          () => {
            if (!that.data.bazaarlist.length)
              that.setData({
                bazaarIsNull: true,
                bazaarHasData: true
              });
          }
        );
        callback && callback();
      })
      .catch(err => {
        callback && callback();
        app.showToast(err?.data?.message || '获取数据失败!请稍后再试');
        this.setData({
          bazaarIsNull: true
        });
      });
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading();
    let {bazaarParms} = that.data;
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: PAGESIZE
    };
    that.setData(
      {
        bazaarParms: obj,
        bazaarlist: [],
        bazaarHasData: true
      },
      () =>
        that.initGetList(() => {
          that.setData({
            bazaarIsTriggered: false
          });
          wx.hideNavigationBarLoading();
        })
    );
  },
  //加载更多
  async bazaarloadMore() {
    if (!app.isLogin()) {
      this.setData({
        count: 5
      });
      return;
    }
    // 判断vip类型 主要是针对普通用户
    if (this.data.bazaarlist.length >= 20) {
      let permission = await hasPrivile({
        packageType: true
      });
      this.setData({
        permission
      });
      if (permission == '普通VIP') return;
    }

    let {bazaarParms, bazaarHasData, bazaarIsFlag} = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData(
      {
        bazaarParms
      },
      () => this.initGetList()
    );
  },
  // 筛选回调
  onFlitter(e) {
    console.log('筛选参数:', e.detail);
  },

  // 数据变化回调
  onDataChange(e) {
    console.log('数据变化:', e.detail);
  },

  // 卡片操作回调
  onCard(e) {
    const {item, type} = e.detail;
    console.log('卡片操作:', type, item);
  },

  // 标题点击回调
  handleTit(e) {
    const {item} = e.detail;
    console.log('标题点击:', item);
  },

  // VIP弹窗
  vipPop() {
    console.log('VIP弹窗');
  },

  // 登录
  login() {
    app.route(this, '/pages/login/login');
  }
});
