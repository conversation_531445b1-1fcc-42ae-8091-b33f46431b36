<!--components/BtmListMultGroupPop/index.wxml-->
<!-- 自定义弹窗容器 -->
<view
  class="custom-popup-mask {{visible ? 'show' : ''}}"
  style="z-index: {{zIndex}};"
  bindtap="close"
>
  <view class="custom-popup-container" catchtap="stopPropagation">
    <view class="btm-list-wrap">
      <!-- Tab 头部 - 使用 chainMap 样式结构 -->
      <view class="tab-header {{activeTabIndex === 2 ? 'chainMap' : ''}}">
        <view class="tabs-wrapper">
          <view
            class="tab-item {{activeTabIndex === index ? 'active' : ''}}"
            wx:for="{{tabs}}"
            wx:key="code"
            data-index="{{index}}"
            bindtap="switchTab"
          >
            <!-- 背景图标 -->
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_tabvip.png"
              class="vip"
              wx:if="{{activeTabIndex === index && item.code !== 'emerging'}}"
            ></image>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_tabzhuanshi.png"
              class="vip"
              wx:if="{{activeTabIndex === index && item.code === 'emerging'}}"
            ></image>
            {{item.name}}
          </view>
        </view>
      </view>

      <!-- 内容容器 -->
      <view class="select-container">
        <!-- 占位内容 -->
        <view wx:if="{{!readyToShow}}" class="placeholder-content">
          <view class="placeholder-text">数据加载中...</view>
        </view>

        <!-- 主要内容 - 只有在准备好时才显示 -->
        <view wx:else class="select-wrap">
          <!-- 父级列表 -->
          <view class="list parent-list">
            <scroll-view scroll-y="true" style="height:100%">
              <view
                class="item parent {{item.active && 'active'}} {{item.selected && 'selected'}}"
                bindtap="selectParent"
                data-code="{{item.code}}"
                data-name="{{item.name}}"
                data-index="{{index}}"
                wx:for="{{parentList}}"
                wx:key="code"
              >
                <text
                  class="{{item.selected && 'selected-text'}}"
                  >{{item.name}}</text
                >
              </view>
            </scroll-view>
          </view>

          <!-- 子级列表 -->
          <view class="list child-list">
            <scroll-view scroll-y="true" style="height:100%">
              <view
                class="item child {{item.selected && 'selected'}}"
                bindtap="selectChild"
                data-code="{{item.code}}"
                data-name="{{item.name}}"
                data-purchased="{{item.purchased}}"
                wx:for="{{activeChildList}}"
                wx:key="code"
              >
                <text
                  class="{{item.selected && 'selected-text'}}"
                  >{{item.name}}</text
                >
                <image
                  class="checkmark {{item.selected && 'show'}}"
                  wx:if="{{item.selected}}"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
                ></image>

                <image
                  class="checkmark {{!item.purchased && 'show'}}"
                  wx:if="{{activeTabIndex === 2 && !item.purchased}}"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_lock.png"
                ></image>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-footer">
      <view class="footer-btn cancel-btn" bindtap="close">取消</view>
      <view class="footer-btn confirm-btn" bindtap="submit">确定</view>
    </view>
  </view>
</view>

<!-- 申请开通产业弹窗 -->
<IndustryApplyPop
  visible="{{vipVisible}}"
  bindclose="onVipClose"
  bindsend="onApplySend"
  zIndex="{{zIndex + 1000}}"
  chain_code="{{currentChainCode}}"
  chain_name="{{currentChainName}}"
/>
