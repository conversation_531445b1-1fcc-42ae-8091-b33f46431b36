.business-list-component {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;

	.page_head {
		background-color: #fff;
		position: relative;
		z-index: 10;
		flex-shrink: 0; // 防止头部被压缩
	}

	.card-box {
		width: 100%;
		position: relative;
		flex: 1; // 占据剩余空间
		overflow: hidden; // 防止内容溢出
	}

	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		color: #999;

		.empty-text {
			font-size: 32rpx;
			margin-bottom: 16rpx;
		}

		.empty-tip {
			font-size: 28rpx;
			color: #ccc;
		}
	}

	.company_num {
		display: flex;
		justify-content: space-between;
		background: #f7f7f7;
		height: 88rpx;
		// border-bottom: 1rpx solid #f0f0f0;
		align-items: center;
		padding: 0 24rpx;

		.text_left {
			font-weight: 400;
			font-size: 28rpx;
			color: #74798c;

			.color_num {
				color: #e72410;
				font-weight: 600;
				padding: 0 8rpx;
			}
		}

		.ditu_mode {
			display: flex;
			align-items: center;

			.img {
				width: 28rpx;
				height: 28rpx;
				margin-right: 8rpx;
			}

			text {
				font-weight: 400;
				font-size: 28rpx;
				color: #1e75db;
			}
		}
	}
}

// 地址弹窗样式
.dialog-con {
	padding: 32rpx;
	text-align: center;

	.map {
		margin: 32rpx 0;
		padding: 24rpx 48rpx;
		background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
		color: #fff;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
	}

	.cancel {
		padding: 24rpx 48rpx;
		background-color: #f8f9fa;
		color: #74798c;
		border-radius: 12rpx;
		font-size: 28rpx;
	}
}

// 工具类
.text-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

// 使用外部样式类来覆盖ComCard的样式
.custom-card-style {
	margin-top: 0 !important;
	margin-bottom: 20rpx !important;
}

// 修复 refresh-scroll 组件的宽度问题
.business-list-component {
	width: 100% !important;

	.refresh-scroll-wrapper {
		width: 100% !important;
		padding: 0 !important;
		margin: 0 !important;
	}

	.refresh-scroll-container {
		width: 100% !important;
		padding-left: 20rpx !important;
		padding-right: 20rpx !important;
		box-sizing: border-box !important;
	}

	// 确保卡片容器宽度正确
	.card-box {
		width: 100% !important;
		padding: 0 !important;
		margin: 0 !important;
	}
}
