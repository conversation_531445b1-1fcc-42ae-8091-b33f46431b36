<view class="enterprise-search-component">
  <!-- 筛选条件 -->
  <DropDownMenu
    height="{{filtrateHeight}}"
    dropDownMenuTitle="{{dropDownMenuTitle}}"
    class="drop-menu"
    bindsubmit="onFlitter"
    heightParams="{{heightParams}}"
    excludeFields="{{excludeFields}}"
    bindvip="vipPop"
  />
  
  <!-- 企业列表 -->
  <view class="enterprise-list">
    <view class="tip" wx:if="{{enterpriseList.length > 0}}">
      共找到<text class="count">{{totalCount}}</text>家企业
    </view>
    
    <refresh-scroll
      id="refresh-scroll"
      container-height="{{computedHeight}}"
      request-url="{{requestFunction}}"
      request-params="{{requestParams}}"
      auto-load="{{false}}"
      empty-text="暂无企业数据"
      empty-tip=""
      bind:datachange="onDataChange"
      bind:scrolltolower="onScrollToLower"
      bind:scroll="onScroll"
    >
      <view slot="content" class="card-list">
        <ComCard
          wx:for="{{enterpriseList}}"
          wx:key="index"
          obj="{{item}}"
          btn="collect"
          bindcardFun="onCardAction"
          bindhandleTit="handleTit"
          hcard="custom-card-style"
          isCheckVip="{{isCheckVip}}"
        />
      </view>
    </refresh-scroll>
  </view>
</view>

<!-- 联系方式弹窗 -->
<Contact visible="{{showContact}}" entId="{{activeEntId}}"></Contact>

<!-- 地址弹窗 -->
<dialog
  visible="{{showAddress}}"
  title="地址"
  isShowConfirm="{{false}}"
  showFooter="{{false}}"
>
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map
        id="map"
        longitude="{{location.lon}}"
        latitude="{{location.lat}}"
        markers="{{addmarkers}}"
        scale="{{11}}"
        style="width: 100%; height: 306rpx;"
      >
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <view bindtap="goMap" class="map"> 导航 </view>
    <view class="cancel" bindtap="onCloseAddress"> 取消 </view>
  </view>
</dialog>

<!-- VIP弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>
