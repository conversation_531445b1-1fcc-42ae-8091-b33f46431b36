<import src="/template/menuhead/index"></import>
<view class="head">
  <template
    is="menu-head"
    data="{{selected_source_name,dropDownMenuTitle,district_open,source_open,filter_open,selected_filter_name,selected_source_name,district_val,source_val,filter_val }}"
  ></template>
</view>

<!-- 具体内容 -->
<!-- 地区 -->
<region-selection
  zIndex="{{1}}"
  top="{{top}}"
  visible="{{district_open}}"
  top="{{top}}"
  bindsubmit="getRegion"
  bindclose="closeRegion"
  oldData="{{area_code_list}}"
/>

<!-- 行业 -->
<industry-select
  wx:if="{{!useIndustrialList}}"
  zIndex="{{1}}"
  visible="{{source_open}}"
  top="{{top}}"
  oldData="{{industry_code_list}}"
  bindsubmit="getIndustry"
  dataType="eleseicAry"
  bindclose="closeRegion"
/>

<!-- 所属产业 -->
<industry-select
  wx:if="{{useIndustrialList}}"
  zIndex="{{1}}"
  visible="{{source_open}}"
  top="{{top}}"
  oldData="{{industrial_list}}"
  bindsubmit="getIndustrialList"
  dataType="industrialAry"
  bindclose="closeRegion"
/>

<!-- 更多筛选 - 使用ConfigurableForm  -->
<view
  class="container container_hd  line-tesu {{filter_open ? 'show' : 'disappear'}}"
  style="height: {{computedHeight}}px;"
>
  <view
    style="height: {{computedHeight-(isIphoneX ? 85:55)}}px; overflow: hidden;"
  >
    <ConfigurableForm
      variant="full"
      excludeFields="{{excludeFields}}"
      wrapHeight="{{computedHeight-(isIphoneX ? 85:55)}}px"
      bindsubmit="onConfigurableFormSubmit"
      bindvip="onConfigurableFormVip"
      id="s-hunt"
    />
  </view>
  <!-- 底部按钮 -->
  <view class="footer" style="height: {{isIphoneX?'85px':'55px'}};">
    <text class="reset" bindtap="resetMoreFilter">重置</text>
    <text bindtap="confirmMoreFilter">确定</text>
  </view>
</view>

<!-- capture-catch:touchmove="preventdefault" -->
