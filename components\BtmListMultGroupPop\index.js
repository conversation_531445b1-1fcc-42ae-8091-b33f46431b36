import constant from '../../utils/constant';
import {
  hotIndustryTypeSelectorApi, // 热点
  classicIndustryExpandListApi, // 经典
  originalIndustryClusterSelectorApi, // 产业图谱
  originalIndustryPurchasedListApi // 产业链图谱 - 已购买的产业链
} from '../../service/industryApi';

// Tab 选项配置
const TAB_OPTIONS = [
  {
    code: 'hot',
    name: '热点产业名单'
  },
  {
    code: 'classic',
    name: '经典产业名单'
  },
  {
    code: 'emerging',
    name: '产业链图谱'
  }
];

const app = getApp();
Component({
  properties: {
    // 弹出层显示/隐藏
    visible: {
      type: Boolean,
      value: false,
      observer(newVal) {
        if (newVal) {
          this.initData();
        }
      }
    },
    // 弹窗位置
    position: {
      type: String,
      value: 'bottom'
    },
    // 弹窗标题
    title: {
      type: String,
      value: '请选择'
    },
    // 上一次选中的数据，用于回填
    oldData: {
      type: Array,
      value: []
    },
    // 数据类型，用于缓存key
    dataType: {
      type: String,
      value: constant.Industrial
    },
    // 标识符，用于事件回调
    mark: {
      type: String,
      value: ''
    },
    // 弹窗层级
    zIndex: {
      type: Number,
      value: 1000
    }
  },

  data: {
    // 新的数据结构 - 兼容 SingleSelect
    parentList: [],
    activeChildList: [],
    selectedPath: {
      parent: null,
      child: null
    },
    finalSelection: null,
    readyToShow: false,
    tabs: TAB_OPTIONS,
    activeTabIndex: 0, // 默认选中第一个tab（热点产业名单）
    currentDataSource: [],
    // 数据源映射
    dataSourceMap: {
      hot: [],
      classic: [],
      emerging: []
    },
    // 选择状态记忆
    confirmedSelection: {
      tabIndex: 0,
      selectedCode: null,
      hasConfirmed: false
    },

    // 保留原有的数据结构以保持兼容性
    sourceData: [], // 原始数据列表
    selectedItems: [], // 已选中的项目
    vipVisible: false, // VIP弹窗显示状态
    currentChainCode: '', // 当前点击的产业链代码
    currentChainName: '' // 当前点击的产业链名称
  },

  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      this.setData({readyToShow: false});

      // 初始化数据源 - 从 API 获取真实数据
      await this.initializeDataSources();

      // 使用默认数据源初始化组件
      this.initializeData(this.data.currentDataSource);

      // 处理回填逻辑
      this.handleOldDataRestore();
    },

    /**
     * 初始化数据源 - 从 API 获取真实数据
     */
    async initializeDataSources() {
      try {
        // 并行请求四个 API
        const [hotData, classicData, originalData, purchasedData] =
          await Promise.all([
            hotIndustryTypeSelectorApi(),
            classicIndustryExpandListApi(),
            originalIndustryClusterSelectorApi(),
            originalIndustryPurchasedListApi()
          ]);

        // 格式化数据并更新组件数据
        const dataSourceMap = {
          hot: this.formatApiDataToComponentFormat(hotData, 'hot'),
          classic: this.formatApiDataToComponentFormat(classicData, 'classic'),
          emerging: [
            ...this.formatApiDataToComponentFormat(
              [
                {
                  hot_industrial_name: '已购买',
                  chains: purchasedData
                }
              ],
              'chainMap'
            ),
            ...this.formatApiDataToComponentFormat(originalData, 'chainMap')
          ]
        };

        // 更新组件数据
        this.setData({
          dataSourceMap,
          currentDataSource: dataSourceMap.hot // 默认使用热点数据
        });
        return dataSourceMap;
      } catch (error) {
        console.error('初始化数据源失败:', error);
        // 如果 API 失败，使用空数据
        const emptyDataSourceMap = {
          hot: [],
          classic: [],
          emerging: []
        };

        this.setData({
          dataSourceMap: emptyDataSourceMap,
          currentDataSource: []
        });

        return emptyDataSourceMap;
      }
    },

    /**
     * 获取产业链数据 (保留原方法以保持兼容性)
     */
    async getAllChainData() {
      try {
        // let allChainData = wx.getStorageSync(this.data.dataType);
        let allChainData = [];
        // if (!allChainData || allChainData.length === 0) {
        // 从API获取数据
        const chain_data = await originalIndustryClusterSelectorApi();
        const tempData = []; // 扁平化数据，只取第一级和第二级
        if (Array.isArray(chain_data)) {
          chain_data.forEach(chainItem => {
            // 添加二级数据
            if (chainItem.child && Array.isArray(chainItem.child)) {
              chainItem.child.forEach(child => {
                tempData.push({
                  code: child.chain_code,
                  name: child.chain_name,
                  active: false,
                  purchased: child.purchased
                });
              });
            }
          });
          allChainData = tempData.sort((a, b) => {
            // 排序：purchased为true的排在前面
            if (a.purchased && !b.purchased) return -1;
            if (!a.purchased && b.purchased) return 1;
            return 0;
          });
          // }
          console.log(allChainData);
          // wx.setStorageSync(this.data.dataType, allChainData);
        }

        this.setData({
          sourceData: allChainData
        });
      } catch (error) {
        console.error('获取产业链数据失败:', error);
      }
    },

    /**
     * 设置选中项（回填数据）
     */
    setSelectedItems() {
      const {oldData, sourceData} = this.data;

      // 先重置所有选中状态
      const resetSourceData = sourceData.map(item => ({
        ...item,
        active: false
      }));
      // 如果没有回填数据，直接设置重置后的状态
      if (!oldData || oldData.length === 0) {
        this.setData({
          sourceData: resetSourceData,
          selectedItems: []
        });
        return;
      }

      // 从 oldData 中提取所有的 code（不管是否有 active 或 status 字段）
      const selectedCodes = new Set();
      oldData.forEach(item => {
        if (item.code) {
          selectedCodes.add(item.code);
        }
      });

      // 在重置后的 sourceData 中查找对应的 code，如果找到就设置为选中
      const updatedSourceData = resetSourceData.map(item => ({
        ...item,
        active: selectedCodes.has(item.code)
      }));

      // 获取选中的项目
      const selectedItems = updatedSourceData.filter(item => item.active);

      this.setData({
        sourceData: updatedSourceData,
        selectedItems
      });
    },

    /**
     * 格式化 API 数据为组件需要的格式
     */
    formatApiDataToComponentFormat(apiData, type) {
      if (!Array.isArray(apiData)) {
        return [];
      }
      return apiData.map(item => {
        const mainName =
          item.hot_industrial_name || item.classic_type_name || item.chain_name;
        const children = item?.chains || item?.child || item?.list;
        return {
          code: item?.code || item?.chain_code || String(item.classic_type_id),
          name: mainName,
          type,
          children: Array.isArray(children)
            ? children.map(chain => {
                const tempObj = {
                  code: `${chain?.code || chain?.chain_code || chain.id}`,
                  name:
                    chain?.name ||
                    chain?.chain_name ||
                    chain.classic_industrial_name,
                  type
                };
                if (Object.keys(chain).includes('purchased')) {
                  tempObj.purchased = chain.purchased;
                }
                return tempObj;
              })
            : []
        };
      });
    },

    /**
     * 初始化数据源：设置父级列表，默认激活第一个父级并显示其子项
     */
    initializeData(allData) {
      // 为每个父级项添加选择状态标记，第一个设为激活状态
      const parentList = allData.map((item, index) => ({
        ...item,
        selected: false, // 是否被最终选中（红色高亮+勾选图标）
        active: index === 0 // 第一个父级默认激活（蓝色高亮，展开子列表）
      }));

      // 获取第一个父级的子列表（如果存在），确保右侧有内容显示
      const firstParent = parentList[0];
      const activeChildList =
        firstParent && firstParent.children
          ? firstParent.children.map(child => ({
              ...child,
              selected: false, // 子项选择状态
              active: false // 子项激活状态
            }))
          : [];

      // 重置组件的所有状态数据
      this.setData({
        parentList, // 左侧父级列表
        activeChildList, // 右侧子级列表（显示第一个父级的子项）
        selectedPath: {
          // 选择路径：记录用户的选择层级
          parent: null, // 选中的父级
          child: null // 选中的子级
        },
        finalSelection: null, // 最终选择结果（用于提交）
        readyToShow: true // 数据准备完毕，可以显示界面
      });
    },

    /**
     * 处理回填数据
     */
    handleOldDataRestore() {
      const {oldData} = this.data;
      if (oldData && oldData.length > 0) {
        // 取第一个选中项进行回填（因为现在是单选）
        const firstSelected = oldData[0];
        if (firstSelected && firstSelected.code) {
          if (firstSelected.tabCode) {
            // 新格式：有Tab信息，直接切换到对应Tab并选中
            this.switchToTabAndSelect(
              firstSelected.tabCode,
              firstSelected.code
            );
          } else {
            // 老格式：智能查找（向后兼容）
            this.setDefaultSelectWithTab(firstSelected.code);
          }
        }
      }
    },

    /**
     * 根据 tabCode 切换到指定Tab并选中指定项（新格式回显）
     */
    switchToTabAndSelect(tabCode, targetCode) {
      // 找到对应的Tab索引
      const tabIndex = TAB_OPTIONS.findIndex(tab => tab.code === tabCode);
      if (tabIndex === -1) {
        console.warn(`未找到对应的Tab: ${tabCode}，使用智能查找`);
        this.setDefaultSelectWithTab(targetCode);
        return;
      }

      const {dataSourceMap} = this.data;
      const newDataSource = dataSourceMap[tabCode];

      // 切换到对应Tab
      this.setData({
        activeTabIndex: tabIndex,
        currentDataSource: newDataSource,
        readyToShow: false
      });

      // 重新初始化数据
      this.initializeData(newDataSource);

      // 延迟设置选中状态
      setTimeout(() => {
        this.setDefaultSelect(targetCode);
      }, 50);
    },

    /**
     * Tab 切换处理
     */
    switchTab(e) {
      const {index} = e.currentTarget.dataset;
      const tabCode = TAB_OPTIONS[index].code;
      const {dataSourceMap} = this.data;
      const newDataSource = dataSourceMap[tabCode];

      // 更新Tab状态和数据源
      this.setData({
        activeTabIndex: index,
        currentDataSource: newDataSource,
        readyToShow: false
      });

      // 重新初始化数据
      this.initializeData(newDataSource);
    },

    /**
     * 智能默认选择：根据code自动查找所属Tab并切换，然后设置选中状态
     */
    setDefaultSelectWithTab(targetCode) {
      if (!targetCode) return;

      // 在所有数据源中查找匹配的code
      let foundTab = null;
      let foundData = null;

      const {dataSourceMap} = this.data;
      for (let tabIndex = 0; tabIndex < TAB_OPTIONS.length; tabIndex++) {
        const tabCode = TAB_OPTIONS[tabIndex].code;
        const dataSource = dataSourceMap[tabCode];

        for (let parent of dataSource) {
          // 检查是否匹配父级code
          if (parent.code === targetCode) {
            foundTab = tabIndex;
            foundData = {parent, child: null};
            break;
          }
          // 检查是否匹配子级code
          if (parent.children) {
            for (let child of parent.children) {
              if (child.code === targetCode) {
                foundTab = tabIndex;
                foundData = {parent, child};
                break;
              }
            }
            if (foundData) break;
          }
        }
        if (foundData) break;
      }

      if (foundTab !== null && foundData) {
        // 切换到对应的Tab
        this.setData({
          activeTabIndex: foundTab,
          currentDataSource: dataSourceMap[TAB_OPTIONS[foundTab].code]
        });

        // 先初始化数据
        this.initializeData(this.data.currentDataSource);

        // 延迟设置默认选择
        setTimeout(() => {
          this.setDefaultSelect(targetCode);
        }, 50);
      } else {
        this.setData({readyToShow: true});
      }
    },

    /**
     * 设置默认选中项
     */
    setDefaultSelect(targetCode) {
      if (!targetCode) return;
      const {parentList} = this.data;
      let selectedPath = {
        parent: null,
        child: null
      };

      // 在当前父级列表中查找匹配的code
      for (let parent of parentList) {
        // 检查父级是否匹配
        if (parent.code === targetCode) {
          selectedPath.parent = parent;
          this._selectParentItemForRestore(parent);
          break;
        }
        // 检查子级是否匹配
        if (parent.children) {
          for (let child of parent.children) {
            if (child.code === targetCode) {
              selectedPath.parent = parent;
              selectedPath.child = child;
              this._selectParentItemForRestore(parent);
              this._selectChildItemForRestore(child, parent);
              break;
            }
          }
          if (selectedPath.child) break;
        }
      }

      // 更新选择状态
      this.setData({
        selectedPath,
        finalSelection: this.getFinalSelection(selectedPath),
        readyToShow: true
      });
    },

    /**
     * 点击列表项 (保留原方法以保持兼容性)
     */
    handleItemClick(e) {
      const {item} = e.currentTarget.dataset;
      const {sourceData, selectedItems} = this.data;
      // 如果没有购买就弹窗
      if (!item.purchased) {
        this.setData({
          vipVisible: true,
          currentChainCode: item.code || '',
          currentChainName: item.name || ''
        });
        return;
      }

      if (selectedItems.length > 4 && !item.active) {
        app.showToast('最多能选五条产业链!', 'none', 2000, false);
        return;
      }

      // 切换选中状态
      const updatedSourceData = sourceData.map(dataItem => {
        if (dataItem.code === item.code) {
          return {
            ...dataItem,
            active: !dataItem.active
          };
        }
        return dataItem;
      });

      // 获取选中的项目
      const checkItems = updatedSourceData.filter(item => item.active);

      this.setData({
        sourceData: updatedSourceData,
        selectedItems: checkItems
      });
    },

    /**
     * 用户点击父级项的事件处理
     */
    selectParent(e) {
      const dataset = e.currentTarget.dataset;
      const {index} = dataset;
      const {parentList} = this.data;
      const selectedParent = parentList[index];
      this.selectParentItem(selectedParent);
    },

    /**
     * 选中父级项的核心逻辑
     */
    selectParentItem(selectedParent) {
      let {parentList} = this.data;

      // 重置所有父级的状态
      parentList = parentList.map(item => ({
        ...item,
        selected: false,
        active: false
      }));

      // 设置当前父级为选中和激活状态
      const parentIndex = parentList.findIndex(
        item => item.code === selectedParent.code
      );
      if (parentIndex !== -1) {
        parentList[parentIndex].selected = true;
        parentList[parentIndex].active = true;
      }

      // 获取选中父级的子列表
      const activeChildList = selectedParent.children
        ? selectedParent.children.map(child => ({
            ...child,
            selected: false
          }))
        : [];

      // 更新组件状态
      this.setData({
        parentList,
        activeChildList,
        selectedPath: {
          parent: selectedParent,
          child: null
        },
        finalSelection: selectedParent
      });
    },

    /**
     * 用户点击子级项的事件处理
     */
    selectChild(e) {
      const dataset = e.currentTarget.dataset;
      const {code, purchased} = dataset;
      if (!purchased && this.data.activeTabIndex === 2) {
        app.showToast('请解锁该产业链图谱后使用', 'none');
        return;
      }

      const {activeChildList} = this.data;
      const selectedChild = activeChildList.find(item => item.code === code);
      if (selectedChild) {
        this.selectChildItem(selectedChild);
      }
    },

    /**
     * 选中子级项的核心逻辑
     */
    selectChildItem(selectedChild) {
      let {parentList, activeChildList} = this.data;

      // 找到子级对应的父级
      let parentOfChild = null;
      for (let parent of parentList) {
        if (
          parent.children &&
          parent.children.some(child => child.code === selectedChild.code)
        ) {
          parentOfChild = parent;
          break;
        }
      }

      if (parentOfChild) {
        // 更新子级状态
        activeChildList = activeChildList.map(item => ({
          ...item,
          selected: item.code === selectedChild.code
        }));

        // 更新父级状态
        parentList = parentList.map(item => ({
          ...item,
          selected: item.code === parentOfChild.code,
          active: item.code === parentOfChild.code
        }));

        // 更新组件状态
        this.setData({
          parentList,
          activeChildList,
          selectedPath: {
            parent: parentOfChild,
            child: selectedChild
          },
          finalSelection: selectedChild
        });
      }
    },

    /**
     * 仅用于恢复状态的父级选择方法
     */
    _selectParentItemForRestore(selectedParent) {
      let {parentList} = this.data;

      parentList = parentList.map(item => ({
        ...item,
        selected: false,
        active: false
      }));

      const parentIndex = parentList.findIndex(
        item => item.code === selectedParent.code
      );
      if (parentIndex !== -1) {
        parentList[parentIndex].selected = true;
        parentList[parentIndex].active = true;
      }

      const activeChildList = selectedParent.children
        ? selectedParent.children.map(child => ({
            ...child,
            selected: false
          }))
        : [];

      this.setData({
        parentList,
        activeChildList
      });
    },

    /**
     * 仅用于恢复状态的子级选择方法
     */
    _selectChildItemForRestore(selectedChild, parentOfChild) {
      let {activeChildList, parentList} = this.data;

      activeChildList = activeChildList.map(item => ({
        ...item,
        selected: item.code === selectedChild.code
      }));

      parentList = parentList.map(item => ({
        ...item,
        selected: item.code === parentOfChild.code,
        active: item.code === parentOfChild.code
      }));

      this.setData({
        parentList,
        activeChildList
      });
    },

    /**
     * 获取最终选择结果
     */
    getFinalSelection(path = null) {
      const selectedPath = path || this.data.selectedPath;
      return selectedPath.child || selectedPath.parent;
    },

    /**
     * 提交选择 (修改为单选逻辑，但保持数据格式兼容)
     */
    submit() {
      const {finalSelection, mark, activeTabIndex} = this.data;

      // 将单选结果转换为兼容多选格式的数组，添加 tabCode 信息
      const checkedList = finalSelection
        ? [
            {
              code: finalSelection.code,
              name: finalSelection.name,
              tabCode: TAB_OPTIONS[activeTabIndex].code, // 新增：记录来源Tab
              status: 'checked',
              active: true
            }
          ]
        : [];

      this.triggerEvent('submits', {
        checkedList,
        mark
      });
    },

    /**
     * 关闭弹窗
     */
    close() {
      const {mark} = this.data;

      // 关闭时也要触发事件，保持原有数据
      this.triggerEvent('submits', {
        checkedList: this.data.oldData,
        mark
      });
    },

    /**
     * VIP弹窗关闭事件
     */
    onVipClose() {
      this.setData({
        vipVisible: false
      });
    },

    /**
     * 申请提交成功事件
     */
    onApplySend() {
      // 关闭弹窗
      this.setData({
        vipVisible: false
      });
      // 可以显示提示信息
      wx.showToast({
        title: '申请已提交',
        icon: 'success'
      });
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击弹窗内容时关闭弹窗
    }
  },
  pageLifetimes: {
    show: function () {
      // 页面显示时初始化数据
      if (this.data.visible) {
        this.initData();
      }
    }
  }
});
